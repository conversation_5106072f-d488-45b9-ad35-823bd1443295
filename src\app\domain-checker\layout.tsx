import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "域名检查工具 - 批量检测域名注册状态 | CatchIdeas",
  description: "专业的域名检查工具，支持单个和批量关键词域名检测，自动生成.com域名并检查注册状态，一键跳转阿里云查询实际可用性。助力域名投资和网站建设。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  keywords: [
    "域名检查",
    "域名注册查询",
    "批量域名检测",
    "域名可用性",
    "关键词域名",
    "域名投资工具",
    "阿里云域名",
    "域名状态查询",
    "域名工具",
    "域名批量查询"
  ],
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/domain-checker",
    title: "域名检查工具 - 批量检测域名注册状态 | CatchIdeas",
    description: "专业的域名检查工具，支持单个和批量关键词域名检测，自动生成.com域名并检查注册状态，一键跳转阿里云查询实际可用性。助力域名投资和网站建设。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "域名检查工具 - 批量检测域名注册状态 | CatchIdeas",
    description: "专业的域名检查工具，支持单个和批量关键词域名检测，自动生成.com域名并检查注册状态，一键跳转阿里云查询实际可用性。助力域名投资和网站建设。",
  },
  alternates: {
    canonical: "https://catchideas.com/domain-checker",
  },
};

export default function DomainCheckerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
